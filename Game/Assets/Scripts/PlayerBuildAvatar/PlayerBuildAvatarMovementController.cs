using ECM2;
using UnityEngine;

public class PlayerBuildAvatarMovementController : BaseMonoBehavior
{
    [HideInInspector]
    public PlayerControlsManager Controls;

    public Transform CameraTarget;
    private Transform CameraTransform;
    private Character Character;
    
    public Vector3 MovementDirection
    {
        get
        {
            var input = Controls.InputVector;
            var look = Controls.LookVector;
            // TODO
        }
    }
    
    public Vector2 InputVector
    {
        get
        {
            return Controls.InputVector;
        }
    }
    
    void Awake()
    {
        if (Controls == null)
        {
            Controls = ControllerInputManager.Instance.Player1ControlsManager;
        }
    }
    
    void Start()
    {
        PlayerCameraManager.Instance.SetPlayerBuildAvatar(CameraTarget);
        CameraTransform = PlayerCameraManager.Instance.MainCamera.transform;
        Character = GetComponent<Character>();
    }

    void Update()
    {
        Character.SetMovementDirection(MovementDirection);
    }

    protected override void OnDestroy()
    {
        PlayerCameraManager.Instance.ClearPlayerBuildAvatar();
        base.OnDestroy();
    }
}
