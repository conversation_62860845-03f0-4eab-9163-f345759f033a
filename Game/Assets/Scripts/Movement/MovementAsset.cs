using UnityEngine;

[CreateAssetMenu(menuName = "Animation/MovementAsset")]
public class MovementAsset : ScriptableObject
{
    public AnimationActionAsset Forward;
    public AnimationActionAsset Backward;
    public AnimationActionAsset Left;
    public AnimationActionAsset Right;
    public AnimationActionAsset Idle;
    public AnimationActionAsset Jump;
    public AnimationActionAsset Fall;
    
    public AnimationActionAsset GetModel(Vector3 movementDirection, bool grounded, Quaternion rotation, Vector3 velocity)
    {
        if (!grounded && velocity.y < -3)
        {
            return Fall;
        }

        if (grounded)
        {
            if (movementDirection.sqrMagnitude < 0.0001f)
                return Idle;

            var localDir = Quaternion.Inverse(rotation) * movementDirection;
            var dir2D   = new Vector2(localDir.x, localDir.z).normalized;
            var angle    = Mathf.Atan2(dir2D.y, dir2D.x);
            var quadrant = Mathf.RoundToInt((angle / (2 * Mathf.PI)) * 4 + 4) % 4;

            // switch (quadrant)
            // {
            //     case 0: return Right;
            //     case 1: return Forward;
            //     case 2: return Left;
            //     case 3: return Backward;
            //     default: return Idle; 
            // }
            switch (quadrant)
            {
                case 0: return Forward;
                case 1: return Forward;
                case 2: return Forward;
                case 3: return Forward;
                default: return Idle; 
            }
        }

        return null;
    }
}
