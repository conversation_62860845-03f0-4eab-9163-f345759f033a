using Pathfinding;
using Pathfinding.RVO;
using UnityEngine;
using Random = UnityEngine.Random;

public class AStarAgent : BaseMonoBehavior
{
    private Vector3 movementDirection;
    public Vector3 MovementDirection => movementDirection;
    
    private bool hasReachedDestination;
    public bool HasReachedDestination => hasReachedDestination;
    
    public float waypointDistance = 0.5f;
    public float destinationThreshold = 1f;
    
    private float startDistance = 0.5f;
    private float repathRate = 0.5f;
    private float fastRepathRate = 0.1f;
    private float fastRepathDistance = 7f;
    private float slowRepathRate = 1f;
    private float slowRepathDistance = 30f;
    private float currentRepathTimer;
    private Seeker seeker;
    private Path currentPath;
    private int currentWaypoint;
    private RVOController rvoController;
    private Combatant combatant;
    private AStarDestination destination;
    private EntityMovementController controller;
    private int modFrame = 300;

    private void Start()
    {
        currentRepathTimer = Random.Range(0f, slowRepathRate);
        modFrame = Random.Range(200, 400);
        combatant = GetComponent<Combatant>();
        seeker = GetComponent<Seeker>();
        rvoController = GetComponent<RVOController>();
        controller = GetComponent<EntityMovementController>();
    }
    
    void Update()
    {
        if (combatant.IsDead)
        {
            return;
        }
        
        if (combatant.ActionManager.PlayingMovementDisablingAction)
        {
            return;
        }
        
        UpdateMovement();
        UpdateRepath();
    }

    private void UpdateMovement()
    {
        if (currentPath == null)
            return;

        var path = currentPath.vectorPath;
        if (path == null || currentWaypoint >= path.Count)
            return;

        Vector3 waypoint = path[currentWaypoint];
        waypoint.y = transform.position.y;
        Vector3 toWaypoint = waypoint - transform.position;
        toWaypoint.y = 0f;
        if (toWaypoint.sqrMagnitude < waypointDistance * waypointDistance)
        {
            currentWaypoint++;
            return;
        }

        Vector3 baseDirection = toWaypoint.normalized;
        Vector3 separation = controller.BoidSeparation.normalized;

        float cancelFactor = Vector3.Dot(baseDirection, separation);
        if (cancelFactor < -0.5f)
        {
            separation *= 0.1f;
        }

        Vector3 combinedMove = baseDirection + separation;
        combinedMove.y = 0f;

        if (combinedMove.sqrMagnitude < 0.001f)
        {
            combinedMove = baseDirection;
        }

        movementDirection = combinedMove.normalized;

        Vector3 finalDestination = path[path.Count - 1];
        CheckIfDestinationReached(finalDestination);
    }
    
    private void UpdateRepath()
    {
        if (!destination.Valid || seeker == null || !seeker.IsDone())
            return;

        var distanceToDestination = Vector3.Distance(transform.position, destination.Destination);
        float targetRepathRate;
        if (distanceToDestination <= fastRepathDistance)
        {
            targetRepathRate = fastRepathRate;
        }
        else if (distanceToDestination > slowRepathDistance)
        {
            targetRepathRate = slowRepathRate;
        }
        else
        {
            targetRepathRate = repathRate;
        }

        currentRepathTimer -= Time.deltaTime;
        if (currentRepathTimer <= 0f)
        {
            currentRepathTimer = targetRepathRate + Random.Range(0f, 0.1f);
            UpdatePath();
        }
    }
    
    void UpdatePath()
    {
        if (combatant.IsDead)
        {
            return;
        }
        if (hasReachedDestination)
        {
            return;
        }

        if (PathCache.TryGetPath(transform.position, destination.Destination, out var cachedPath))
        {
            UpdatePath(cachedPath);
            return;
        }
        
        if (!destination.Valid || !seeker.IsDone()) return;
        seeker.StartPath(transform.position, destination.Destination, OnPathComplete);
    }

    void OnPathComplete(Path path)
    {
        if (path.error)
        {
            Debug.Log(path.errorLog);
            return;
        }

        UpdatePath(path);
        PathCache.AddPath(transform.position, destination.Destination, path);
    }

    private void UpdatePath(Path path)
    {
        currentPath = path;
        currentWaypoint = 0;
        hasReachedDestination = false;
        
        var first = path.vectorPath[0];
        var agentPos = transform.position;
        first.y = 0f;
        agentPos.y = 0f;
        if ((first - agentPos).sqrMagnitude < waypointDistance)
        {
            // skip redundant first point
            currentWaypoint = 1; 
        }
        else
        {
            currentWaypoint = 0;
        }
    }
    
    void CheckIfDestinationReached(Vector3 finalDestination)
    {
        Vector3 agentPos = transform.position;
        agentPos.y = 0f;

        finalDestination.y = 0f;
        Vector3 delta = agentPos - finalDestination;
        float sqrDistance = delta.sqrMagnitude;
        float thresholdSq = destinationThreshold * destinationThreshold;
        float startThresholdSq = (startDistance + destinationThreshold) * (startDistance + destinationThreshold);

        if (hasReachedDestination)
        {
            if (sqrDistance > startThresholdSq)
            {
                hasReachedDestination = false;
            }
            else
            {
                movementDirection = Vector3.zero;
            }
        }
        else
        {
            if (sqrDistance <= thresholdSq)
            {
                hasReachedDestination = true;
                destination = default;
                currentPath = null;
                currentWaypoint = 0;
                movementDirection = Vector3.zero;
            }
        }
    }

    public void SetDestination(Vector3 newDestination)
    {
        this.destination = new AStarDestination(newDestination);
        hasReachedDestination = false;
        CheckIfDestinationReached(newDestination);
    }

    public void ClearDestination()
    {
        destination = default;
        hasReachedDestination = true;
        movementDirection = Vector3.zero;
        currentPath = null;
        currentWaypoint = 0;
    }

    public void MoveToRandomPoint()
    {
        if (Time.frameCount % modFrame == 0)
        {
            var point = GetRandomPoint();
            if (point.node != null)
            {
                SetDestination(point.position);
            }
        }
    }
    
    private NNInfo GetRandomPoint()
    {
        return AstarPath.active.graphs[0].RandomPointOnSurface(NNConstraint.Walkable);
    }
}

struct AStarDestination
{
    public Vector3 Destination;
    public bool Valid;

    public AStarDestination(Vector3 destination)
    {
        Destination = destination;
        Valid = true;
    }
}
