using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class PlayerDodgeBehavior : BaseMonoBehavior
{
    private Combatant combatant;

    void Start()
    {
        combatant = GetComponent<Combatant>();
    }

    public void TryDodge(Vector3 movementDirection)
    {
        if (!enabled)
        {
            return;
        }

        if (combatant.IsDead)
        {
            return;
        }

        if (!combatant.IsGrounded)
        {
            return;
        }

        float min = 0.1f;
        if (Mathf.Abs(movementDirection.x) < min && Mathf.Abs(movementDirection.y) < min && Mathf.Abs(movementDirection.z) < min)
        {
            DodgeBack();
        }
        else
        {
            DodgeForward();
        }
    }

    private void DodgeForward()
    {
        PerformDodge(combatant.EquipmentManager.Weapon.Animation.DodgeForward);
    }

    private void DodgeBack()
    {
        PerformDodge(combatant.EquipmentManager.Weapon.Animation.DodgeBackwards);
    }

    private void PerformDodge(AnimationActionAsset action)
    {
        var model = combatant.ActionManager.Execute(action, true);
        if (model == null)
        {
            var currentAction = combatant.ActionManager.Current;
            if (currentAction != null && currentAction.CanSetNextAction)
            {
                currentAction.NextAction = action.GenerateModel();
            }
        }
    }
}
