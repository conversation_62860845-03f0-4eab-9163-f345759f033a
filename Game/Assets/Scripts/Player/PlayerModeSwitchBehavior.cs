using System;
using UnityEngine;

public class PlayerModeSwitchBehavior : BaseMonoBehavior
{
    public GameObject PlayerBuildAvatar;
    private GameObject CurrentAvatar;
    
    [HideInInspector]
    public PlayerMode CurrentMode;

    public bool IsPlayer => CurrentMode == PlayerMode.Player;
    
    public void Switch()
    {
        if (CurrentMode == PlayerMode.Build)
        {
            SwitchToPlayer();
        }
        else
        {
            SwitchToBuild();
        }
    }

    private void SwitchToPlayer()
    {
        DestroyAvatar();
        CurrentMode = PlayerMode.Player;
    }

    private void SwitchToBuild()
    {
        CurrentMode = PlayerMode.Build;
        var spawnPosition = new Vector3(transform.position.x, transform.position.y + 2, transform.position.z);
        CurrentAvatar = Instantiate(PlayerBuildAvatar, spawnPosition, transform.rotation);
    }

    private void DestroyAvatar()
    {
        if (CurrentAvatar != null)
        {
            Destroy(CurrentAvatar);
        }
    }

    protected override void OnDestroy()
    {
        DestroyAvatar();
        base.OnDestroy();
    }
}
