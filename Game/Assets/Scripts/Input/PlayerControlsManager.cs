using UnityEngine;
using UniRx;
using UnityEngine.InputSystem;

public class PlayerControlsManager
{
    public PlayerControls Controls { get; private set; }

    private Subject<Unit> onInputAttack = new Subject<Unit>();
    private Subject<Unit> onInputBlock = new Subject<Unit>();
    private Subject<Unit> onInputDodgeRoll = new Subject<Unit>();
    private Subject<Unit> onInputJump = new Subject<Unit>();
    private Subject<Unit> onInputNpcSwitchLeft = new Subject<Unit>();
    private Subject<Unit> onInputNpcSwitchRight = new Subject<Unit>();
    private Subject<Unit> onInputSpecial = new Subject<Unit>();
    private Subject<Vector2> onInputMenuNavigation = new Subject<Vector2>();
    private Subject<Unit> onInputMenuSubmit = new Subject<Unit>();
    private Subject<Unit> onInputMenuCancel = new Subject<Unit>();
    private Subject<Unit> onInputModeSwitch = new Subject<Unit>();

    public System.IObservable<Unit> OnInputAttack => onInputAttack;
    public System.IObservable<Unit> OnInputBlock => onInputBlock;
    public System.IObservable<Unit> OnInputDodgeRoll => onInputDodgeRoll;
    public System.IObservable<Unit> OnInputJump => onInputJump;
    public System.IObservable<Unit> OnInputNpcSwitchLeft => onInputNpcSwitchLeft;
    public System.IObservable<Unit> OnInputNpcSwitchRight => onInputNpcSwitchRight;
    public System.IObservable<Unit> OnInputSpecial => onInputSpecial;

    public System.IObservable<Vector2> OnInputMenuNavigation => onInputMenuNavigation;
    public System.IObservable<Unit> OnInputMenuSubmit => onInputMenuSubmit;
    public System.IObservable<Unit> OnInputMenuCancel => onInputMenuCancel;
    public System.IObservable<Unit> OnInputModeSwitch => onInputModeSwitch;

    private ButtonHoldHelper attackHoldHelper;
    private ButtonHoldHelper blockHoldHelper;

    public bool AttackHold => attackHoldHelper.Holding;
    public bool BlockHold => blockHoldHelper.Holding;

    public bool PlayerDisabled = false;
    public bool UIDisabled = true;

    public Vector2 InputVector
    {
        get
        {
            if (PlayerDisabled)
            {
                return Vector2.zero;
            }

            return Controls.Player.Movement.ReadValue<Vector2>();
        }
    }

    public Vector2 LookVector
    {
        get
        {
            if (PlayerDisabled)
            {
                return Vector2.zero;
            }
            return Controls.Player.Look.ReadValue<Vector2>();
        }
    }

    public Vector2 UIInputVector
    {
        get
        {
            if (UIDisabled)
            {
                return Vector2.zero;
            }
            return Controls.UI.Navigate.ReadValue<Vector2>();
        }
    }

    public PlayerControlsManager()
    {
        Controls = new PlayerControls();
        Controls.Enable();

        attackHoldHelper = new ButtonHoldHelper(Controls.Player.Attack);
        blockHoldHelper = new ButtonHoldHelper(Controls.Player.Block);

        SetupAttack();
        SetupBlock();
        SetupDodge();
        SetupJump();
        SetupSpecial();
        SetupModeSwitch();
        SetupMenuSubmit();
        SetupMenuCancel();
    }

    private void SetupAttack()
    {
        Controls.Player.Attack.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputAttack.OnNext(Unit.Default);
        };
    }

    private void SetupBlock()
    {
        Controls.Player.Block.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputBlock.OnNext(Unit.Default);
        };
    }

    private void SetupDodge()
    {
        Controls.Player.Dodge.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputDodgeRoll.OnNext(Unit.Default);
        };
    }

    private void SetupJump()
    {
        Controls.Player.Jump.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputJump.OnNext(Unit.Default);
        };
    }

    private void SetupSpecial()
    {
        Controls.Player.Special.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputSpecial.OnNext(Unit.Default);
        };
    }
    
    private void SetupModeSwitch()
    {
        Controls.Player.ModeSwitch.performed += o =>
        {
            if (!CanTrigger(o))
            {
                return;
            }

            onInputModeSwitch.OnNext(Unit.Default);
        };
    }

    private void HandleMenuNavigation()
    {
        if (UIDisabled)
        {
            return;
        }

        var value = UIInputVector;
        if (value != Vector2.zero && value.magnitude > 0.3f)
        {
            onInputMenuNavigation.OnNext(value);
        }
    }

    private void SetupMenuSubmit()
    {
        Controls.UI.Submit.performed += o =>
        {
            if (!CanTriggerUI(o))
            {
                return;
            }

            onInputMenuSubmit.OnNext(Unit.Default);
        };
    }

    private void SetupMenuCancel()
    {
        Controls.UI.Cancel.performed += o =>
        {
            if (!CanTriggerUI(o))
            {
                return;
            }

            onInputMenuCancel.OnNext(Unit.Default);
        };
    }

    public void Update()
    {
        attackHoldHelper.Update();
        blockHoldHelper.Update();
        HandleMenuNavigation();
    }

    public void FixedUpdate()
    {

    }

    public void OnEnable()
    {
        Controls.Enable();
    }

    public void OnDisable()
    {
        Controls.Disable();
    }

    public void SetToUI()
    {
        PlayerDisabled = true;
        UIDisabled = false;
        Controls.UI.Enable();
        Controls.Player.Disable();
    }

    public void SetToPlayer()
    {
        PlayerDisabled = false;
        UIDisabled = true;
        Controls.Player.Enable();
        Controls.UI.Disable();
    }

    private bool CanTrigger(InputAction.CallbackContext context)
    {
        if (context.ReadValue<float>() < Constants.TriggerSensitivity)
        {
            return false;
        }

        if (PlayerDisabled)
        {
            return false;
        }

        return true;
    }

    private bool CanTriggerUI(InputAction.CallbackContext context)
    {
        if (context.ReadValue<float>() < Constants.TriggerSensitivity)
        {
            return false;
        }

        if (UIDisabled)
        {
            return false;
        }

        return true;
    }
}
