using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using UnityEngine;
using Unity.Collections;
using Unity.Transforms;
using TerrainCollider = UnityEngine.TerrainCollider;

public class TerrainColliderAuthoring : MonoBehaviour
{
}

public class TerrainColliderBaker : Baker<TerrainColliderAuthoring>
{
    public override void Bake(TerrainColliderAuthoring authoring)
    {
        var unityCollider = authoring.GetComponent<TerrainCollider>();
        if (unityCollider == null)
        {
            Debug.LogWarning("Missing Terrain or TerrainCollider.");
            return;
        }

        var terrainData = unityCollider.terrainData;
        int resolution = terrainData.heightmapResolution;

        // Get heightmap and flatten to 1D
        float[,] heights2D = terrainData.GetHeights(0, 0, resolution, resolution);
        var heights1D = new NativeArray<float>(resolution * resolution, Allocator.Temp);
        for (int z = 0; z < resolution; z++)
        {
            for (int x = 0; x < resolution; x++)
            {
                heights1D[z * resolution + x] = heights2D[z, x];
            }
        }

        // Compute correct scale (spacing) per Unity.Physics docs
        float3 scale = new float3(
            terrainData.size.x / (resolution - 1),
            terrainData.size.y,
            terrainData.size.z / (resolution - 1)
        );

        // Build collision filter from Unity layer
        int unityLayer = authoring.gameObject.layer;
        uint belongsTo = 1u << unityLayer;
        uint collidesWith = LayerHelper.GetCollidesWithMask(unityLayer); // your helper method

        var filter = new CollisionFilter
        {
            BelongsTo = belongsTo,
            CollidesWith = collidesWith,
            GroupIndex = 0
        };

        var collider = Unity.Physics.TerrainCollider.Create(
            heights1D,
            new int2(resolution, resolution),
            scale,
            Unity.Physics.TerrainCollider.CollisionMethod.Triangles,
            filter
        );

        heights1D.Dispose();

        if (!collider.IsCreated)
        {
            Debug.LogError("TerrainCollider was not created properly.");
            return;
        }

        var entity = GetEntity(TransformUsageFlags.Renderable);

        AddComponent(entity, new LocalTransform
        {
            Position = authoring.transform.position,
            Rotation = authoring.transform.rotation,
            Scale = 1f
        });

        AddComponent(entity, new PhysicsCollider { Value = collider });
        AddComponent<Simulate>(entity);
        AddComponent<PhysicsWorldIndex>(entity);
    }
}
