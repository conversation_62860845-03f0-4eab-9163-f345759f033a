#if UNITY_EDITOR
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine.SceneManagement;

public static class SubsceneColliderGenerator
{
    [MenuItem("Tools/Generate Subscene Colliders")]
    public static void Generate()
    {
        Scene activeScene = SceneManager.GetActiveScene();
        string subsceneName = activeScene.name + "Subscene";

        Scene subscene = SceneManager.GetSceneByName(subsceneName);
        if (!subscene.IsValid())
        {
            Debug.LogError($"Subscene '{subsceneName}' not found. Make sure it's open.");
            return;
        }

        var allGameObjects = activeScene.GetRootGameObjects()
            .Where(root => root.activeInHierarchy)
            .SelectMany(g => g.GetComponentsInChildren<Transform>(false))
            .Where(t => t.gameObject.activeInHierarchy)
            .Select(t => t.gameObject);
        
        var subsceneObjects = subscene.GetRootGameObjects()
            .SelectMany(g => g.GetComponentsInChildren<Transform>(true))
            .Select(t => t.gameObject)
            .Where(go => go.name.EndsWith("SubsceneCollider"))
            .ToList();

        foreach (var obj in subsceneObjects)
        {
            Object.DestroyImmediate(obj);
        }
        
        foreach (var go in allGameObjects)
        {
            bool hasMarker = go.GetComponent<GenerateSubsceneCollider>() != null;
            bool isGroundLayer = go.layer == LayerHelper.GroundLayer;

            if (hasMarker || isGroundLayer)
            {
                // Create a new GameObject with same transform
                GameObject copy = new GameObject(go.name + "SubsceneCollider");
                copy.transform.position = go.transform.position;
                copy.transform.rotation = go.transform.rotation;
                copy.transform.localScale = go.transform.lossyScale;
                copy.layer = go.layer;

                foreach (var collider in go.GetComponents<Collider>())
                {
                    ComponentUtility.CopyComponent(collider);
                    ComponentUtility.PasteComponentAsNew(copy);
                }
                
                // If original had a TerrainCollider, add TerrainColliderAuthoring
                if (go.GetComponent<TerrainCollider>() != null)
                {
                    if (copy.GetComponent<TerrainCollider>() == null)
                    {
                        Debug.LogWarning($"Expected TerrainCollider to be copied onto {copy.name}, but none found.");
                    }

                    if (copy.GetComponent<TerrainColliderAuthoring>() == null)
                    {
                        copy.AddComponent<TerrainColliderAuthoring>();
                    }
                }
                
                // Move to subscene and register undo
                SceneManager.MoveGameObjectToScene(copy, subscene);
                Undo.RegisterCreatedObjectUndo(copy, "Generate Collider Copy");
            }
        }
        
        // GenerateTrees(subscene);
        Debug.Log("Collider copies generated in subscene.");
    }

    private static void GenerateTrees(Scene subscene)
    {
        Terrain[] terrains = GameObject.FindObjectsOfType<Terrain>();
        foreach (var terrain in terrains)
        {
            var terrainData = terrain.terrainData;
            var treePrototypes = terrainData.treePrototypes;
            var treeInstances = terrainData.treeInstances;

            for (int i = 0; i < treeInstances.Length; i++)
            {
                var tree = treeInstances[i];
                var prototype = treePrototypes[tree.prototypeIndex];
                var prefab = prototype.prefab;
                if (!prefab) continue;

                // Set reasonable defaults (override if known)
                float trunkRadius = 0.25f;
                float trunkHeight = 2f;

                var renderer = prefab.GetComponentInChildren<Renderer>();
                if (renderer)
                {
                    var meshBounds = renderer.bounds;
                    trunkHeight = meshBounds.size.y * tree.heightScale * 0.5f; // Trunk height estimate
                    trunkRadius = Mathf.Min(meshBounds.size.x, meshBounds.size.z) * tree.widthScale * 0.25f;
                }

                // Clamp to reasonable values
                trunkHeight = Mathf.Clamp(trunkHeight, 0.5f, 10f);
                trunkRadius = Mathf.Clamp(trunkRadius, 0.1f, 1f);

                Vector3 worldPos = Vector3.Scale(tree.position, terrainData.size) + terrain.transform.position;
                Quaternion rotation = Quaternion.identity;

                GameObject treeCollider = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                treeCollider.name = $"Tree{i}SubsceneCollider";

                // Center base of capsule at ground level
                treeCollider.transform.position = worldPos + new Vector3(0, trunkHeight * 0.5f, 0);
                treeCollider.transform.rotation = rotation;
                treeCollider.transform.localScale = new Vector3(trunkRadius * 2f, trunkHeight * 0.5f, trunkRadius * 2f);
                treeCollider.layer = LayerHelper.GroundLayer;

                Object.DestroyImmediate(treeCollider.GetComponent<MeshRenderer>());
                Object.DestroyImmediate(treeCollider.GetComponent<MeshFilter>());

                SceneManager.MoveGameObjectToScene(treeCollider, subscene);
                // Undo.RegisterCreatedObjectUndo(treeCollider, "Generate Tree Collider");
            }
        }
    }
}
#endif
