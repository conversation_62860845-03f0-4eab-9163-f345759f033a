using Unity.Collections;
using Unity.Entities;
using Unity.Physics;
using Unity.Transforms;
using UnityEngine;

[ExecuteAlways]
public class TreeColliderDebugger : MonoBehaviour
{
    void OnDrawGizmos()
    {
        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
        var treeQuery = entityManager.CreateEntityQuery(
            ComponentType.ReadOnly<LocalTransform>()
        );

        using var entities = treeQuery.ToEntityArray(Allocator.Temp);
        foreach (var entity in entities)
        {
            var localTransform = entityManager.GetComponentData<LocalTransform>(entity);
            Gizmos.color = Color.yellow;
            Gizmos.DrawSphere(localTransform.Position, 0.5f);
        }
    }
}
