using System;
using UnityEngine;
using UniRx;

public class ActionManager : BaseMonoBehavior
{
    private float currentActionTime = 0f;
    private Combatant combatant;
    private BehaviorSubject<AnimationActionModel> currentSubject = new BehaviorSubject<AnimationActionModel>(null);

    private System.IObservable<AnimationActionModel> actionObservable;
    public System.IObservable<AnimationActionModel> ActionObservable => actionObservable;
    public AnimationActionModel Current => currentSubject.Value;

    public bool PlayingMovementDisablingAction
    {
        get
        {
            if (Current != null)
            {
                return Current.MovementDisabling && !Current.SoftFinish;
            }
            else
            {
                return false;
            }
        }
    }

    private void Awake()
    {
        actionObservable = currentSubject.Where(o => o != null); 
    }

    void Start()
    {
        combatant = GetComponent<Combatant>();
        HandleEvents();
    }

    private void HandleEvents()
    {
        AddDisposable(combatant.AnimationManager.eventStream.Subscribe(OnAnimationEvent, Debug.LogError));
    }
    
    private void OnAnimationEvent(string evt)
    {
        var action = Current;
        if (action == null) return;
        action.OnAnimationEvent(evt, combatant);
        switch (evt)
        {
            case Constants.SoftFinishEvent:
                action.SoftFinish = true;
                HandleNextAction();
                break;
            case Constants.SoftFinishActionEvent:
                if (combatant.IsPlayer)
                {
                    action.SoftFinishAction = true;
                }
                HandleNextActionEarly();
                break;
            case Constants.ComboEvent:
                action.Combo = true;
                HandleComboActionEarly();
                break;
            case Constants.SoftStartEvent:
                action.SoftStart = true;
                break;
        }
    }
    
    private void HandleComboActionEarly()
    {
        var current = Current;
        if (current == null)
            return;

        var next = current.NextAction;
        if (next != null && next.ComboInterrupter)
        {
            Execute(next);
        }
    }

    private void Update()
    {
        HandleActionTime();
    }

    private void HandleActionTime()
    {
        if (Current == null)
        {
            return;
        }

        if (Current.AcceptInput)
        {
            return;
        }

        if (currentActionTime > Current.AcceptInputDelay)
        {
            Current.AcceptInput = true;
        }

        currentActionTime += Time.deltaTime;
    }

    public AnimationModel Execute(AnimationActionAsset action, bool hardForce = false)
    {
        if (action == null)
        {
            return null;
        }
        
        return Execute(action.GenerateModel(), hardForce);
    }

    public AnimationModel Execute(AnimationActionModel animationActionModel, bool hardForce = false)
    {
        if (animationActionModel == null)
        {
            return null;
        }

        if (Current != null && !hardForce)
        {
            if (Current.DeathAction)
            {
                AnimationActionModelPool.Release(animationActionModel);
                return null;
            }

            if (!CurrentActionIsInteruptable(animationActionModel))
            {
                AnimationActionModelPool.Release(animationActionModel);
                return null;
            }

            if (Current.ActionAsset == animationActionModel.ActionAsset && !Current.SoftFinish)
            {
                if (Current.NextAction != animationActionModel)
                {
                    AnimationActionModelPool.Release(animationActionModel);
                }
                return null;
            }
        }

        var model = animationActionModel.Play(combatant);
        if (model != null)
        {
            Debug.Log(model.animationAsset.name);
            ClearAction();
            currentSubject.OnNext(animationActionModel);
            currentActionTime = 0f;
            if (!model.Looping)
            {
                model.OnAnimationComplete = HandleAnimationComplete;
            }
        }

        return model;
    }

    public bool CurrentActionIsInteruptable()
    {
        if (Current == null)
        {
            return true;
        }

        if (Current.Interuptable)
        {
            return true;
        }

        if (Current.SoftFinish)
        {
            return true;
        }

        return false;
    }

    public bool CurrentActionIsInteruptable(AnimationActionModel nextAction) {
        if (CurrentActionIsInteruptable())
        {
            return true;
        }

        if (Current.ForceInteruptable && nextAction.Force)
        {
            return true;
        }

        if (Current.SoftStartInteruptable && !Current.SoftStart && nextAction.SoftStartInterrupter)
        {
            return true;
        }

        if (Current.SoftFinishAction && nextAction.SoftFinishActionInterrupter)
        {
            return true;
        }

        if (Current.Combo && nextAction.ComboInterrupter)
        {
            return true;
        }

        return false;
    }

    public bool CurrentActionIsInvulnerable()
    {
        if (Current == null)
        {
            return false;
        }

        return Current.Invulnerable;
    }

    public bool CurrentActionIsBlocking()
    {
        if (Current == null)
        {
            return false;
        }

        return Current.Blocking;
    }

    private void ClearAction()
    {
        var old = Current;
        if (old != null)
        {
            AnimationActionModelPool.Release(old);
        }
        currentSubject.OnNext(null);
    }
    
    protected override void OnDestroy()
    {
        var old = Current;
        if (old != null)
        {
            AnimationActionModelPool.Release(old);
        }
        base.OnDestroy();
    }
    
    private void HandleAnimationComplete()
    {
        var current = Current;
        if (current == null || current.DeathAction)
            return;

        var next = current.NextAction;
        current.MakeInteruptable = true;
        if (next != current && next != null)
        {
            Execute(next);
        }
    }
    
    private void HandleNextAction()
    {
        var current = Current;
        if (current == null || combatant.IsNPC)
            return;

        var next = current.NextAction;
        if (current != next && next != null)
        {
            Execute(next);
        }
    }
    
    private void HandleNextActionEarly()
    {
        if (Current == null)
        {
            return;
        }

        if (combatant.IsNPC)
        {
            return;
        }

        if (Current.NextAction != null && Current.NextAction.SoftFinishActionInterrupter)
        {
            HandleNextAction();
        }
    }
}
